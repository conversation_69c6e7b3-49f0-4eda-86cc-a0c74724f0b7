<?php

return [
  /*
  |--------------------------------------------------------------------------
  | Validation Language Lines
  |--------------------------------------------------------------------------
  |
  | The following language lines contain the default error messages used by
  | the validator class. Some of these rules have multiple versions such
  | as the size rules. Feel free to tweak each of these messages.
  |
  */

  'accepted' => 'Trường :attribute phải được chấp nhận.',
  'active_url' => 'Trường :attribute không phải là một URL hợp lệ.',
  'after' => 'Trường :attribute phải là một ngày sau ngày :date.',
  'after_or_equal' => 'Trường :attribute phải là thời gian bắt đầu sau hoặc đúng bằng :date.',
  'alpha' => 'Trường :attribute chỉ có thể chứa các chữ cái.',
  'alpha_dash' => 'Trường :attribute chỉ có thể chứa chữ cái, số và dấu gạch ngang.',
  'alpha_num' => 'Trường :attribute chỉ có thể chứa chữ cái và số.',
  'array' => 'Trường :attribute phải là dạng mảng.',
  'before' => 'Trường :attribute phải là một ngày trước ngày :date.',
  'before_or_equal' => 'Trường :attribute phải là thời gian bắt đầu trước hoặc đúng bằng :date.',
  'between' => [
    'numeric' => 'Trường :attribute phải nằm trong khoảng :min - :max.',
    'file' => 'Dung lượng tập tin trong trường :attribute phải từ :min - :max kB.',
    'string' => 'Trường :attribute phải từ :min - :max ký tự.',
    'array' => 'Trường :attribute phải có từ :min - :max phần tử.',
  ],
  'boolean' => 'Trường :attribute phải là true hoặc false.',
  'confirmed' => 'Giá trị xác nhận trong trường :attribute không khớp.',
  'date' => 'Trường :attribute không phải là định dạng của ngày-tháng.',
  'date_format' => 'Trường :attribute không giống với định dạng :format.',
  'different' => 'Trường :attribute và :other phải khác nhau.',
  'digits' => 'Độ dài của trường :attribute phải gồm :digits chữ số.',
  'digits_between' => 'Độ dài của trường :attribute phải nằm trong khoảng :min and :max chữ số.',
  'dimensions' => 'Trường :attribute có kích thước không hợp lệ.',
  'distinct' => 'Trường :attribute có giá trị trùng lặp.',
  'email' => 'Trường :attribute phải là một địa chỉ email hợp lệ.',
  'exists' => 'Giá trị đã chọn trong trường :attribute không hợp lệ.',
  'file' => 'Trường :attribute phải là một tệp tin.',
  'filled' => 'Trường :attribute không được bỏ trống.',
  'gt' => [
    'numeric' => 'Giá trị trường :attribute phải lớn hơn :value.',
    'file' => 'Dung lượng trường :attribute phải lớn hơn :value kilobytes.',
    'string' => 'Độ dài trường :attribute phải nhiều hơn :value kí tự.',
    'array' => 'Mảng :attribute phải có nhiều hơn :value phần tử.',
  ],
  'gte' => [
    'numeric' => 'Giá trị trường :attribute phải lớn hơn hoặc bằng :value.',
    'file' => 'Dung lượng trường :attribute phải lớn hơn hoặc bằng :value kilobytes.',
    'string' => 'Độ dài trường :attribute phải lớn hơn hoặc bằng :value kí tự.',
    'array' => 'Mảng :attribute phải có ít nhất :value phần tử.',
  ],
  'image' => 'Trường :attribute phải là định dạng hình ảnh.',
  'in' => 'Giá trị đã chọn trong trường :attribute không hợp lệ.',
  'in_array' => 'Trường :attribute phải thuộc tập cho phép: :other.',
  'integer' => 'Trường :attribute phải là một số nguyên.',
  'ip' => 'Trường :attribute phải là một địa chỉ IP.',
  'ipv4' => 'Trường :attribute phải là một địa chỉ IPv4.',
  'ipv6' => 'Trường :attribute phải là một địa chỉ IPv6.',
  'json' => 'Trường :attribute phải là một chuỗi JSON.',
  'lt' => [
    'numeric' => 'Giá trị trường :attribute phải nhỏ hơn :value.',
    'file' => 'Dung lượng trường :attribute phải nhỏ hơn :value kilobytes.',
    'string' => 'Độ dài trường :attribute phải nhỏ hơn :value kí tự.',
    'array' => 'Mảng :attribute phải có ít hơn :value phần tử.',
  ],
  'lte' => [
    'numeric' => 'Giá trị trường :attribute phải nhỏ hơn hoặc bằng :value.',
    'file' => 'Dung lượng trường :attribute phải nhỏ hơn hoặc bằng :value kilobytes.',
    'string' => 'Độ dài trường :attribute phải nhỏ hơn hoặc bằng :value kí tự.',
    'array' => 'Mảng :attribute không được có nhiều hơn :value phần tử.',
  ],
  'max' => [
    'numeric' => 'Trường :attribute không được lớn hơn :max.',
    'file' => 'Dung lượng tập tin trong trường :attribute không được lớn hơn :max kB.',
    'string' => 'Trường :attribute không được lớn hơn :max ký tự.',
    'array' => 'Trường :attribute không được lớn hơn :max phần tử.',
  ],
  'mimes' => 'Trường :attribute phải là một tập tin có định dạng: :values.',
  'mimetypes' => 'Trường :attribute phải là một tập tin có định dạng: :values.',
  'min' => [
    'numeric' => 'Trường :attribute phải tối thiểu là :min.',
    'file' => 'Dung lượng tập tin trong trường :attribute phải tối thiểu :min kB.',
    'string' => 'Trường :attribute phải có tối thiểu :min ký tự.',
    'array' => 'Trường :attribute phải có tối thiểu :min phần tử.',
  ],
  'not_in' => 'Giá trị đã chọn trong trường :attribute không hợp lệ.',
  'not_regex' => 'Trường :attribute có định dạng không hợp lệ.',
  'numeric' => 'Trường :attribute phải là một số.',
  'present' => 'Trường :attribute phải được cung cấp.',
  'regex' => 'Trường :attribute có định dạng không hợp lệ.',
  'required' => 'Trường :attribute không được bỏ trống.',
  'required_if' => 'Trường :attribute không được bỏ trống khi trường :other là :value.',
  'required_unless' => 'Trường :attribute không được bỏ trống trừ khi :other là :values.',
  'required_with' => 'Trường :attribute không được bỏ trống khi một trong :values có giá trị.',
  'required_with_all' => 'Trường :attribute không được bỏ trống khi tất cả :values có giá trị.',
  'required_without' => 'Trường :attribute không được bỏ trống khi một trong :values không có giá trị.',
  'required_without_all' => 'Trường :attribute không được bỏ trống khi tất cả :values không có giá trị.',
  'same' => 'Trường :attribute và :other phải giống nhau.',
  'size' => [
    'numeric' => 'Trường :attribute phải bằng :size.',
    'file' => 'Dung lượng tập tin trong trường :attribute phải bằng :size kB.',
    'string' => 'Trường :attribute phải chứa :size ký tự.',
    'array' => 'Trường :attribute phải chứa :size phần tử.',
  ],
  'string' => 'Trường :attribute phải là một chuỗi ký tự.',
  'timezone' => 'Trường :attribute phải là một múi giờ hợp lệ.',
  'unique' => 'Trường :attribute đã có trong cơ sở dữ liệu.',
  'uploaded' => 'Trường :attribute tải lên thất bại.',
  'url' => 'Trường :attribute không giống với định dạng một URL.',
  'failed' => 'Xác thực không thành công',
  /*
  |--------------------------------------------------------------------------
  | Custom Validation Language Lines
  |--------------------------------------------------------------------------
  |
  | Here you may specify custom validation messages for attributes using the
  | convention "attribute.rule" to name the lines. This makes it quick to
  | specify a specific custom language line for a given attribute rule.
  |
  */

  'custom' => [
    'name' => [
      'required' => 'Trường tên là bắt buộc',
      'unique' => 'Tên đã tồn tại',
      'min' => 'Tên phải có ít nhất 3 ký tự',
      'max' => 'Tên không được quá 255 ký tự',
    ],
    'email' => [
      'required' => 'Trường email là bắt buộc',
      'unique' => 'Email đã tồn tại',
      'email' => 'Email không hợp lệ',
    ],
    'password' => [
      'required' => 'Trường mật khẩu là bắt buộc',
      'min' => 'Mật khẩu phải có ít nhất 6 ký tự',
      'max' => 'Mật khẩu không được quá 255 ký tự',
      'confirmed' => 'Mật khẩu không khớp',
    ],
    'password_confirmation' => [
      'required' => 'Trường xác nhận mật khẩu là bắt buộc',
      'min' => 'Mật khẩu phải có ít nhất :min ký tự',
    ],
    'phone' => [
      'required' => 'Trường số điện thoại là bắt buộc',
      'unique' => 'Số điện thoại đã tồn tại',
      'min' => 'Số điện thoại phải có ít nhất 10 ký tự',
      'max' => 'Số điện thoại không được quá 15 ký tự',
      'regex' => 'Số điện thoại không hợp lệ',
    ],
    'address' => [
      'required' => 'Trường địa chỉ là bắt buộc',
      'min' => 'Địa chỉ phải có ít nhất 10 ký tự',
      'max' => 'Địa chỉ không được quá 255 ký tự',
    ],
    'roles' => [
      'required' => 'Trường vai trò là bắt buộc',
      'unique' => 'Vai trò đã tồn tại',
      'min' => 'Vai trò phải có ít nhất 3 ký tự',
      'max' => 'Vai trò không được quá 255 ký tự',
    ],
    'permission' => [
      'required' => 'Trường quyền là bắt buộc',
      'unique' => 'Quyền đã tồn tại',
      'array' => 'Quyền không hợp lệ'
    ],
    'site_name' => [
      'max' => 'Tên website không được quá 255 ký tự'
    ],
    'site_meta_title' => [
      'max' => 'Tiêu đề website không được quá 255 ký tự'
    ],
    'site_url' => [
      'url' => 'Đường dẫn website không hợp lệ'
    ],
    'site_email' => [
      'email' => 'Email không hợp lệ'
    ],
    'logo' => [
      'image' => 'File không phải là ảnh',
      'mimes' => 'File không đúng định dạng ảnh',
      'max' => 'File ảnh quá lớn'
    ],
    'favicon' => [
      'image' => 'File không phải là ảnh',
      'mimes' => 'File không đúng định dạng ảnh',
      'max' => 'File ảnh quá lớn'
    ],
    'smtp_host' => [
      'required' => 'Trường host là bắt buộc',
      'max' => 'SMTP Host không được quá 255 ký tự'
    ],
    'smtp_port' => [
      'required' => 'Trường port là bắt buộc',
      'numeric' => 'Port không hợp lệ'
    ],
    'smtp_encryption' => [
      'required' => 'Trường encryption là bắt buộc',
      'in' => 'Encryption không hợp lệ'
    ],
    'smtp_username' => [
      'required' => 'Trường username là bắt buộc',
      'email' => 'Username không hợp lệ'
    ],
    'smtp_password' => [
      'required' => 'Trường password là bắt buộc',
      'max' => 'Password không được quá 255 ký tự'
    ],
    'smtp_sender_email' => [
      'required' => 'Trường sender email là bắt buộc',
      'email' => 'Sender email không hợp lệ'
    ],
    'smtp_sender_name' => [
      'required' => 'Trường sender name là bắt buộc',
    ],
    'smtp_email_test' => [
      'required' => 'Trường email test là bắt buộc',
      'email' => 'Email không hợp lệ'
    ],
    'push_app_id' => [
      'required' => 'Trường app id là bắt buộc',
      'max' => 'App id không được quá 255 ký tự'
    ],
    'push_app_key' => [
      'required' => 'Trường app key là bắt buộc',
      'max' => 'App key không được quá 255 ký tự'
    ],
    'push_secret_key' => [
      'required' => 'Trường secret key là bắt buộc',
      'max' => 'Secret key không được quá 255 ký tự'
    ],
    'push_cluster' => [
      'required' => 'Trường cluster là bắt buộc',
      'max' => 'Cluster không được quá 255 ký tự'
    ],
    'reverb_app_id' => [
      'required' => 'Trường app id là bắt buộc',
      'max' => 'App id không được quá 255 ký tự'
    ],
    'reverb_secret_key' => [
      'required' => 'Trường secret key là bắt buộc',
      'max' => 'Secret key không được quá 255 ký tự'
    ],
    'reverb_host' => [
      'required' => 'Trường host là bắt buộc',
      'max' => 'Host không được quá 255 ký tự'
    ],
    'reverb_port' => [
      'required' => 'Trường port là bắt buộc',
      'numeric' => 'Port không hợp lệ',
      'max' => 'Port không được quá 255 ký tự'
    ],
    'reverb_scheme' => [
      'required' => 'Trường scheme là bắt buộc',
      'max' => 'Scheme không hợp lệ'
    ],
    'vnpt_public_service_endpoint' => [
      'required' => 'Trường endpoint là bắt buộc',
      'max' => 'Endpoint không được quá 255 ký tự'
    ],
    'vnpt_portal_service_endpoint' => [
      'required' => 'Trường endpoint là bắt buộc',
      'max' => 'Endpoint không được quá 255 ký tự'
    ],
    'vnpt_biz_service_endpoint' => [
      'required' => 'Trường endpoint là bắt buộc',
      'max' => 'Endpoint không được quá 255 ký tự'
    ],
    'vnpt_username' => [
      'required' => 'Trường username là bắt buộc',
      'max' => 'Username không được quá 255 ký tự'
    ],
    'vnpt_password' => [
      'required' => 'Trường password là bắt buộc',
      'max' => 'Password không được quá 255 ký tự'
    ],
    'vnpt_account_username' => [
      'required' => 'Trường username là bắt buộc',
      'max' => 'Username không được quá 255 ký tự'
    ],
    'vnpt_account_password' => [
      'required' => 'Trường password là bắt buộc',
      'max' => 'Password không được quá 255 ký tự'
    ],
    'vnpt_pattern' => [
      'required' => 'Trườngpattern là bắt buộc',
      'max' => 'Pattern không được quá 20 ký tự'
    ],
    'vnpt_serial' => [
      'required' => 'Trường serial là bắt buộc',
      'max' => 'Serial không được quá 20 ký tự'
    ],
    'vnpt_sandbox_public_service_endpoint' => [
      'required' => 'Trường endpoint là bắt buộc',
      'max' => 'Endpoint không được quá 255 ký tự'
    ],
    'vnpt_sandbox_portal_service_endpoint' => [
      'required' => 'Trường endpoint là bắt buộc',
      'max' => 'Endpoint không được quá 255 ký tự'
    ],
    'vnpt_sandbox_biz_service_endpoint' => [
      'required' => 'Trường endpoint là bắt buộc',
      'max' => 'Endpoint không được quá 255 ký tự'
    ],
    'vnpt_sandbox_username' => [
      'required' => 'Trường username là bắt buộc',
      'max' => 'Username không được quá 255 ký tự'
    ],
    'vnpt_sandbox_password' => [
      'required' => 'Trường password là bắt buộc',
      'max' => 'Password không được quá 255 ký'
    ],
    'vnpt_sandbox_account_username' => [
      'required' => 'Trường username là bắt buộc',
      'max' => 'Username không được quá 255 ký tự'
    ],
    'vnpt_sandbox_account_password' => [
      'required' => 'Trường password là bắt buộc',
      'max' => 'Password không được quá 255 ký tự'
    ],
    'vnpt_sandbox_pattern' => [
      'required' => 'Trường pattern là bắt buộc',
      'max' => 'Pattern không được quá 20 ký tự'
    ],
    'vnpt_sandbox_serial' => [
      'required' => 'Trường serial là bắt buộc',
      'max' => 'Serial không được quá 20 ký tự'
    ],
    'nhattin_username' => [
      'required' => 'Trường username là bắt buộc',
      'max' => 'Username không được quá 255 ký tự'
    ],
    'nhattin_password' => [
      'required' => 'Trường password là bắt buộc',
      'max' => 'Password không được quá 255 ký tự'
    ],
    'nhattin_service_id' => [
      'required' => 'Trường service id là bắt buộc',
      'max' => 'Service id không được quá 255 ký tự'
    ],
    'nhattin_payment_method' => [
      'required' => 'Trường payment method là bắt buộc',
      'max' => 'Payment method không được quá 255 ký tự'
    ],
    'nhattin_cod' => [
      'required' => 'Trường cod là bắt buộc',
      'max' => 'Cod không được quá 255 ký tự'
    ],
    'nhattin_cargo_type' => [
      'required' => 'Trường cargo type là bắt buộc',
      'max' => 'Cargo type không được quá 255 ký tự'
    ],
    'nhattin_sandbox_username' => [
      'required' => 'Trường username là bắt buộc',
      'max' => 'Username không được quá 255 ký tự'
    ],
    'nhattin_sandbox_password' => [
      'required' => 'Trường password là bắt buộc',
      'max' => 'Password không được quá 255 ký tự'
    ],
    'nhattin_sandbox_service_id' => [
      'required' => 'Trường service id là bắt buộc',
      'max' => 'Service id không được quá 255 ký tự'
    ],
    'nhattin_sandbox_payment_method' => [
      'required' => 'Trường payment method là bắt buộc',
      'max' => 'Payment method không được quá 255 ký tự'
    ],
    'nhattin_sandbox_cod' => [
      'required' => 'Trường cod là bắt buộc',
      'max' => 'Cod không được quá 255 ký tự'
    ],
    'nhattin_sandbox_cargo_type' => [
      'required' => 'Trường cargo type là bắt buộc',
      'max' => 'Cargo type không được quá 255 ký tự'
    ],
    'nhattin_sender_name' => [
      'required' => 'Trường sender name là bắt buộc',
      'max' => 'Sender name không được quá 255 ký tự'
    ],
    'nhattin_sender_phone' => [
      'required' => 'Trường sender phone là bắt buộc',
      'max' => 'Sender phone không được quá 255 ký tự'
    ],
    'nhattin_sender_address' => [
      'required' => 'Trường sender address là bắt buộc',
      'max' => 'Sender address không được quá 255 ký tự'
    ],
    'nhattin_ufm_source' => [
      'required' => 'Trường ufm source là bắt buộc',
      'max' => 'Ufm source không được quá 255 ký tự'
    ],
    'nhattin_host' => [
      'required' => 'Trường host là bắt buộc',
      'max' => 'Host không được quá 255 ký tự',
      'url' => 'Host không đúng định dạng'
    ],
    'nhattin_sandbox_host' => [
      'required' => 'Trường host là bắt buộc',
      'max' => 'Host không được quá 255 ký tự',
      'url' => 'Host không đúng định dạng'
    ],
    'nhattin_parner_id' => [
      'required' => 'Trường parner id là bắt buộc',
      'max' => 'Parner id không được quá 255 ký tự',
      'alpha_num' => 'Parner id không đúng định dạng'
    ],
    'nhattin_sandbox_parner_id' => [
      'required' => 'Trường parner id là bắt buộc',
      'max' => 'Parner id không được quá 255 ký tự',
      'alpha_num' => 'Parner id không đúng định dạng'
    ],
    'sap_server' => [
      'required' => 'Trường server là bắt buộc',
      'max' => 'Server không được quá 255 ký tự'
    ],
    'sap_database' => [
      'required' => 'Trường database là bắt buộc',
      'max' => 'Database không được quá 255 ký tự'
    ],
    'sap_username' => [
      'required' => 'Trường username là bắt buộc',
      'max' => 'Username không được quá 255 ký tự'
    ],
    'sap_password' => [
      'required' => 'Trường password là bắt buộc',
      'max' => 'Password không được quá 255 ký tự'
    ],
    'sap_sandbox_server' => [
      'required' => 'Trường server là bắt buộc',
      'max' => 'Server không được quá 255 ký tự'
    ],
    'sap_sandbox_database' => [
      'required' => 'Trường database là bắt buộc',
      'max' => 'Database không được quá 255 ký tự'
    ],
    'sap_sandbox_username' => [
      'required' => 'Trường username là bắt buộc',
      'max' => 'Username không được quá 255 ký tự'
    ],
    'sap_sandbox_password' => [
      'required' => 'Trường password là bắt buộc',
      'max' => 'Password không được quá 255 ký tự'
    ],
    'zalo_host' => [
      'required' => 'Trường host là bắt buộc',
      'max' => 'Host không được quá 255 ký tự',
      'url' => 'Host không đúng định dạng'
    ],
    'zalo_redirect_url' => [
      'required' => 'Trường redirect url là bắt buộc',
      'max' => 'Redirect url không được quá 255 ký tự',
      'url' => 'Redirect url không đúng định dạng'
    ],
    'zalo_app_id' => [
      'required' => 'Trường app id là bắt buộc',
      'max' => 'App id không được quá 255 ký tự'
    ],
    'zalo_app_secret' => [
      'required' => 'Trường app secret là bắt buộc',
      'max' => 'App secret không được quá 255 ký tự'
    ],
    'zalo_code_verifier' => [
      'required' => 'Trường code verifier là bắt buộc',
      'max' => 'Code verifier không được quá 255 ký tự'
    ],
    'asus_host' => [
      'required' => 'Trường host là bắt buộc',
      'max' => 'Host không được quá 255 ký tự',
      'url' => 'Host không đúng định dạng'
    ],
    'asus_token' => [
      'required' => 'Trường token là bắt buộc',
      'max' => 'Token không được quá 255 ký tự'
    ],
    'asus_sandbox_host' => [
      'required' => 'Trường host là bắt buộc',
      'max' => 'Host không được quá 255 ký tự',
      'url' => 'Host không đúng định dạng'
    ],
    'asus_sandbox_token' => [
      'required' => 'Trường token là bắt buộc',
      'max' => 'Token không được quá 255 ký tự'
    ],
  ],

  /*
  |--------------------------------------------------------------------------
  | Custom Validation Attributes
  |--------------------------------------------------------------------------
  |
  | The following language lines are used to swap attribute place-holders
  | with something more reader friendly such as E-Mail Address instead
  | of "email". This simply helps us make messages a little cleaner.
  |
  */

  'attributes' => [
  ],
];
