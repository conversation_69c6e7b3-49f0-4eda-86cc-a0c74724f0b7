{"pages": [{"name": "Dashboard Analytics", "icon": "ri-bar-chart-line", "url": "/"}, {"name": "Dashboard CRM", "icon": "ri-donut-chart-line", "url": "dashboard/crm"}, {"name": "Layout Collapsed menu", "icon": "ri-layout-top-line", "url": "layouts/collapsed-menu"}, {"name": "Layout Content navbar", "icon": "ri-layout-5-line", "url": "layouts/content-navbar"}, {"name": "Layout Content nav + Sidebar", "icon": "ri-layout-2-line", "url": "layouts/content-nav-sidebar"}, {"name": "Layout Without menu", "icon": "ri-layout-4-line", "url": "layouts/without-menu"}, {"name": "Layout Without navbar", "icon": "ri-layout-left-line", "url": "layouts/without-navbar"}, {"name": "Layout Fluid", "icon": "ri-layout-top-line", "url": "layouts/fluid"}, {"name": "Layout Container", "icon": "ri-layout-top-2-line", "url": "layouts/container"}, {"name": "Layout Blank", "icon": "ri-square-line", "url": "layouts/blank"}, {"name": "User Management", "icon": "ri-account-circle-line", "url": "laravel/user-management"}, {"name": "Email", "icon": "ri-mail-line", "url": "app/email"}, {"name": "Cha<PERSON>", "icon": "ri-message-line", "url": "app/chat"}, {"name": "Calendar", "icon": "ri-calendar-line", "url": "app/calendar"}, {"name": "Ka<PERSON><PERSON>", "icon": "ri-drag-drop-line", "url": "app/kanban"}, {"name": "eCommerce Dashboard", "icon": "ri-shopping-cart-2-line", "url": "app/ecommerce/dashboard"}, {"name": "eCommerce - Product", "icon": "ri-building-line", "url": "app/ecommerce/product/list"}, {"name": "eCommerce - Product List", "icon": "ri-file-list-line", "url": "app/ecommerce/product/list"}, {"name": "eCommerce - Add Product", "icon": "ri-add-line", "url": "app/ecommerce/product/add"}, {"name": "eCommerce - Category List", "icon": "ri-list-unordered", "url": "app/ecommerce/product/category"}, {"name": "eCommerce - Order List", "icon": "ri-list-unordered", "url": "app/ecommerce/order/list"}, {"name": "eCommerce - Orders Details", "icon": "ri-play-list-line", "url": "app/ecommerce/order/details"}, {"name": "eCommerce - Customers", "icon": "ri-user-line", "url": "app/ecommerce/customer/all"}, {"name": "eCommerce - Customers Overview", "icon": "ri-list-check", "url": "app/ecommerce/customer/details/overview"}, {"name": "eCommerce - Customers Security", "icon": "ri-shield-cross-line", "url": "app/ecommerce/customer/details/security"}, {"name": "eCommerce - Customers Address and Billing", "icon": "ri-map-pin-5-line", "url": "app/ecommerce/customer/details/billing"}, {"name": "eCommerce - Customers Notifications", "icon": "ri-notification-badge-line", "url": "app/ecommerce/customer/details/notifications"}, {"name": "eCommerce - Manage Reviews", "icon": "ri-message-line", "url": "app/ecommerce/manage/reviews"}, {"name": "eCommerce - Referrals", "icon": "ri-group-line", "url": "app/ecommerce/referrals"}, {"name": "eCommerce - Settings Store Details", "icon": "ri-store-line", "url": "app/ecommerce/settings/details"}, {"name": "eCommerce - Settings Store Payments", "icon": "ri-cash-fill", "url": "app/ecommerce/settings/payments"}, {"name": "eCommerce - Settings Store Checkout", "icon": "ri-shopping-cart-2-line", "url": "app/ecommerce/settings/checkout"}, {"name": "eCommerce - Settings Shipping & Delivery", "icon": "ri-truck-line", "url": "app/ecommerce/settings/shipping"}, {"name": "eCommerce - Settings Locations", "icon": "ri-map-pin-add-line", "url": "app/ecommerce/settings/locations"}, {"name": "eCommerce - Settings Notifications", "icon": "ri-notification-4-line", "url": "app/ecommerce/settings/notifications"}, {"name": "Academy - Dashboard", "icon": "ri-book-open-line", "url": "app/academy/dashboard"}, {"name": "Academy - My Course", "icon": "ri-list-unordered", "url": "app/academy/course"}, {"name": "Academy - Course Details", "icon": "ri-play-circle-line", "url": "app/academy/course-details"}, {"name": "User List", "icon": "ri-group-line", "url": "app/user/list"}, {"name": "User View - Account", "icon": "ri-eye-line", "url": "app/user/view/account"}, {"name": "User View - Security", "icon": "ri-shield-user-line", "url": "app/user/view/security"}, {"name": "User View - Billing & Plans", "icon": "ri-bill-line", "url": "app/user/view/billing"}, {"name": "User View - Notifications", "icon": "ri-notification-badge-line", "url": "app/user/view/notifications"}, {"name": "User View - Connections", "icon": "ri-links-line", "url": "app/user/view/connections"}, {"name": "Roles", "icon": "ri-shield-user-line", "url": "app/access-roles"}, {"name": "Permission", "icon": "ri-shield-user-line", "url": "app/access-permission"}, {"name": "Logistics Dashboard", "icon": "ri-truck-line", "url": "app/logistics/dashboard"}, {"name": "Logistics Fleet", "icon": "ri-car-line", "url": "app/logistics/fleet"}, {"name": "Invoice List", "icon": "ri-list-ordered-2", "url": "app/invoice/list"}, {"name": "Invoice Preview", "icon": "ri-article-line", "url": "app/invoice/preview"}, {"name": "Invoice Edit", "icon": "ri-file-edit-line", "url": "app/invoice/edit"}, {"name": "Invoice Add", "icon": "ri-file-add-line", "url": "app/invoice/add"}, {"name": "User Profile", "icon": "ri-user-settings-line", "url": "pages/profile-user"}, {"name": "User Profile - Teams", "icon": "ri-team-line", "url": "pages/profile-teams"}, {"name": "User Profile - Projects", "icon": "ri-account-circle-line", "url": "pages/profile-projects"}, {"name": "User Profile - Connections", "icon": "ri-links-line", "url": "pages/profile-connections"}, {"name": "Account <PERSON><PERSON><PERSON> - Account", "icon": "ri-user-settings-line", "url": "pages/account-settings-account"}, {"name": "Account <PERSON> - Security", "icon": "ri-lock-unlock-line", "url": "pages/account-settings-security"}, {"name": "Account Settings - Billing & Plans", "icon": "ri-money-dollar-circle-line", "url": "pages/account-settings-billing"}, {"name": "Account Settings - Notifications", "icon": "ri-notification-3-line", "url": "pages/account-settings-notifications"}, {"name": "Account <PERSON><PERSON><PERSON> - Connections", "icon": "ri-link", "url": "pages/account-settings-connections"}, {"name": "FAQ", "icon": "ri-question-line", "url": "pages/faq"}, {"name": "Pricing", "icon": "ri-money-dollar-circle-line", "url": "pages/pricing"}, {"name": "Error", "icon": "ri-error-warning-line", "url": "pages/misc-error"}, {"name": "Under Maintenance", "icon": "ri-settings-2-line", "url": "pages/misc-under-maintenance"}, {"name": "Coming Soon", "icon": "ri-time-line", "url": "pages/misc-comingsoon"}, {"name": "Not Authorized", "icon": "ri-group-line", "url": "pages/misc-not-authorized"}, {"name": "Server Error", "icon": "ri-server-fill", "url": "pages/misc-server-error"}, {"name": "Login Basic", "icon": "ri-login-box-line", "url": "auth/login-basic"}, {"name": "<PERSON>gin <PERSON>", "icon": "ri-login-box-line", "url": "auth/login-cover"}, {"name": "Register Basic", "icon": "ri-user-add-line", "url": "auth/register-basic"}, {"name": "Register Cover", "icon": "ri-user-add-line", "url": "auth/register-cover"}, {"name": "Register Multi-steps", "icon": "ri-user-add-line", "url": "auth/register-multisteps"}, {"name": "Verify Email Basic", "icon": "ri-mail-check-line", "url": "auth/verify-email-basic"}, {"name": "Verify Email Cover", "icon": "ri-mail-check-line", "url": "auth/verify-email-cover"}, {"name": "Reset Password Basic", "icon": "ri-rotate-lock-line", "url": "auth/reset-password-basic"}, {"name": "Reset Password Cover", "icon": "ri-rotate-lock-line", "url": "auth/reset-password-cover"}, {"name": "Forgot Password Basic", "icon": "ri-lock-line", "url": "auth/forgot-password-basic"}, {"name": "Forgot Password Cover", "icon": "ri-lock-line", "url": "auth/forgot-password-cover"}, {"name": "Two Steps Verification Basic", "icon": "ri-macbook-line", "url": "auth/two-steps-basic"}, {"name": "Two Steps Verification Cover", "icon": "ri-macbook-line", "url": "auth/two-steps-cover"}, {"name": "Help Center Front", "icon": "ri-question-line", "url": "front-pages/help-center"}, {"name": "Landing Front", "icon": "ri-article-line", "url": "front-pages/landing"}, {"name": "Pricing Front", "icon": "ri-money-dollar-circle-line", "url": "front-pages/pricing"}, {"name": "Checkout Front", "icon": "ri-shopping-cart-2-line", "url": "front-pages/checkout"}, {"name": "Payment Front", "icon": "ri-bank-card-line", "url": "front-pages/payment"}, {"name": "Modal Examples", "icon": "ri-square-line", "url": "modal-examples"}, {"name": "Checkout Wizard", "icon": "ri-shopping-cart-2-line", "url": "wizard/ex-checkout"}, {"name": "Property Listing Wizard", "icon": "ri-shopping-cart-2-line", "url": "wizard/ex-property-listing"}, {"name": "Create Deal Wizard", "icon": "ri-shopping-cart-2-line", "url": "wizard/ex-create-deal"}, {"name": "Icons", "icon": "ri-remixicon-line", "url": "icons/icons-ri"}, {"name": "Basic Cards", "icon": "ri-square-line", "url": "cards/basic"}, {"name": "Advance Cards", "icon": "ri-list-check-2", "url": "cards/advance"}, {"name": "Statistics Cards", "icon": "ri-bar-chart-box-line", "url": "cards/statistics"}, {"name": "Analytics Cards", "icon": "ri-line-chart-fill", "url": "cards/analytics"}, {"name": "Gamifications Cards", "icon": "ri-gamepad-line", "url": "cards/gamifications"}, {"name": "Actions Cards", "icon": "ri-keyboard-line", "url": "cards/actions"}, {"name": "Accordion", "icon": "ri-align-vertically", "url": "ui/accordion"}, {"name": "<PERSON><PERSON><PERSON>", "icon": "ri-alert-line", "url": "ui/alerts"}, {"name": "Badges", "icon": "ri-notification-4-line", "url": "ui/badges"}, {"name": "Buttons", "icon": "ri-square-line", "url": "ui/buttons"}, {"name": "Carousel", "icon": "ri-carousel-view", "url": "ui/carousel"}, {"name": "Collapse", "icon": "ri-aspect-ratio-line", "url": "ui/collapse"}, {"name": "Dropdowns", "icon": "ri-menu-line", "url": "ui/dropdowns"}, {"name": "Footer", "icon": "ri-layout-bottom-line", "url": "ui/footer"}, {"name": "List Groups", "icon": "ri-list-unordered", "url": "ui/list-groups"}, {"name": "Modals", "icon": "ri-square-line", "url": "ui/modals"}, {"name": "<PERSON><PERSON><PERSON>", "icon": "ri-layout-top-line", "url": "ui/navbar"}, {"name": "<PERSON><PERSON><PERSON>", "icon": "ri-layout-top-2-line", "url": "ui/offcanvas"}, {"name": "Pagination & Breadcrumbs", "icon": "ri-skip-right-line", "url": "ui/pagination-breadcrumbs"}, {"name": "Progress", "icon": "ri-donut-chart-line", "url": "ui/progress"}, {"name": "Spinners", "icon": "ri-donut-chart-line", "url": "ui/spinners"}, {"name": "Tabs & Pills", "icon": "ri-window-2-line", "url": "ui/tabs-pills"}, {"name": "Toasts", "icon": "ri-message-line", "url": "ui/toasts"}, {"name": "Tooltips & Popovers", "icon": "ri-chat-quote-line", "url": "ui/tooltips-popovers"}, {"name": "Typography", "icon": "ri-font-size", "url": "ui/typography"}, {"name": "Avatar", "icon": "ri-user-smile-line", "url": "extended/ui-avatar"}, {"name": "BlockUI", "icon": "ri-fullscreen-line", "url": "extended/ui-blockui"}, {"name": "Drag & Drop", "icon": "ri-file-copy-line", "url": "extended/ui-drag-and-drop"}, {"name": "Media Player", "icon": "ri-music-2-fill", "url": "extended/ui-media-player"}, {"name": "Perfect Scrollbar", "icon": "ri-arrow-up-down-line", "url": "extended/ui-perfect-scrollbar"}, {"name": "Star Ratings", "icon": "ri-star-line", "url": "extended/ui-star-ratings"}, {"name": "SweetAlert2", "icon": "ri-share-circle-line", "url": "extended/ui-sweetalert2"}, {"name": "Text Divider", "icon": "ri-align-vertically", "url": "extended/ui-text-divider"}, {"name": "Timeline Basic", "icon": "ri-git-commit-line", "url": "extended/ui-timeline-basic"}, {"name": "Timeline Fullscreen", "icon": "ri-git-commit-line", "url": "extended/ui-timeline-fullscreen"}, {"name": "Tour", "icon": "ri-send-plane-2-line", "url": "extended/ui-tour"}, {"name": "Treeview", "icon": "ri-git-fork-line ri-rotate-180", "url": "extended/ui-treeview"}, {"name": "Miscellaneous", "icon": "ri-organization-chart", "url": "extended/ui-misc"}, {"name": "Basic Inputs", "icon": "ri-pencil-line", "url": "forms/basic-inputs"}, {"name": "Input groups", "icon": "ri-checkbox-line", "url": "forms/input-groups"}, {"name": "Custom Options", "icon": "ri-list-check-3", "url": "forms/custom-options"}, {"name": "Editors", "icon": "ri-file-edit-line", "url": "forms/editors"}, {"name": "File Upload", "icon": "ri-upload-2-line", "url": "forms/file-upload"}, {"name": "Pickers", "icon": "ri-calendar-todo-line", "url": "forms/pickers"}, {"name": "Select & Tags", "icon": "ri-list-check", "url": "forms/selects"}, {"name": "Sliders", "icon": "ri-equalizer-line", "url": "forms/sliders"}, {"name": "Switches", "icon": "ri-toggle-line", "url": "forms/switches"}, {"name": "Extras", "icon": "ri-add-box-line", "url": "forms/extras"}, {"name": "Vertical Form", "icon": "ri-file-text-line", "url": "form/layouts-vertical"}, {"name": "Horizontal Form", "icon": "ri-file-text-line", "url": "form/layouts-horizontal"}, {"name": "Sticky Actions", "icon": "ri-file-text-line", "url": "form/layouts-sticky"}, {"name": "Numbered Wizard", "icon": "ri-align-center", "url": "form/wizard-numbered"}, {"name": "Icons Wizard", "icon": "ri-align-center", "url": "form/wizard-icons"}, {"name": "Form Validation", "icon": "ri-check-line", "url": "form/validation"}, {"name": "Tables", "icon": "ri-window-2-line", "url": "tables/basic"}, {"name": "Datatable Basic", "icon": "ri-grid-line", "url": "tables/datatables-basic"}, {"name": "Datatable Advanced", "icon": "ri-grid-line", "url": "tables/datatables-advanced"}, {"name": "Datatable Extensions", "icon": "ri-grid-line", "url": "tables/datatables-extensions"}, {"name": "Apex Charts", "icon": "ri-line-chart-line", "url": "charts/apex"}, {"name": "ChartJS", "icon": "ri-bar-chart-grouped-fill", "url": "charts/chartjs"}, {"name": "Leaflet Maps", "icon": "ri-map-2-line", "url": "maps/leaflet"}], "files": [{"name": "Class Attendance", "subtitle": "By <PERSON>", "src": "img/icons/misc/search-xls.png", "meta": "17kb", "url": "javascript:;"}, {"name": "Passport Image", "subtitle": "By <PERSON>", "src": "img/icons/misc/search-jpg.png", "meta": "35kb", "url": "javascript:;"}, {"name": "Class Notes", "subtitle": "By <PERSON>", "src": "img/icons/misc/search-doc.png", "meta": "153kb", "url": "javascript:;"}, {"name": "Receipt", "subtitle": "By <PERSON><PERSON>", "src": "img/icons/misc/search-jpg.png", "meta": "25kb", "url": "javascript:;"}, {"name": "Social Guide", "subtitle": "By <PERSON>", "src": "img/icons/misc/search-doc.png", "meta": "39kb", "url": "javascript:;"}, {"name": "Expenses", "subtitle": "By <PERSON>", "src": "img/icons/misc/search-xls.png", "meta": "15kb", "url": "javascript:;"}, {"name": "Documentation", "subtitle": "By <PERSON>", "src": "img/icons/misc/search-doc.png", "meta": "200kb", "url": "javascript:;"}, {"name": "Avatar", "subtitle": "<PERSON> <PERSON>", "src": "img/icons/misc/search-jpg.png", "meta": "100kb", "url": "javascript:;"}, {"name": "Data", "subtitle": "By <PERSON>", "src": "img/icons/misc/search-xls.png", "meta": "5kb", "url": "javascript:;"}, {"name": "Gardening Guide", "subtitle": "By <PERSON>", "src": "img/icons/misc/search-doc.png", "meta": "25kb", "url": "javascript:;"}], "members": [{"name": "<PERSON>", "subtitle": "Admin", "src": "img/avatars/1.png", "url": "app/user/view/account"}, {"name": "<PERSON><PERSON><PERSON>", "subtitle": "Customer", "src": "img/avatars/2.png", "url": "app/user/view/account"}, {"name": "<PERSON><PERSON>", "subtitle": "Staff", "src": "img/avatars/5.png", "url": "app/user/view/account"}, {"name": "<PERSON>", "subtitle": "Staff", "src": "img/avatars/7.png", "url": "app/user/view/account"}, {"name": "<PERSON>", "subtitle": "Customer", "src": "img/avatars/3.png", "url": "app/user/view/account"}, {"name": "<PERSON>", "subtitle": "Admin", "src": "img/avatars/10.png", "url": "app/user/view/account"}, {"name": "<PERSON>", "subtitle": "Admin", "src": "img/avatars/12.png", "url": "app/user/view/account"}]}