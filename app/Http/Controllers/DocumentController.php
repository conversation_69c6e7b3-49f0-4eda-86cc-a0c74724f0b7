<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\ContractType;
use App\Models\AssetTemplate;
use App\Models\DocumentParty;
use App\Models\DocumentAsset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DocumentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Check permission
        if (!Auth::user()->hasPermission('documents.view')) {
            abort(403, 'Bạn không có quyền xem danh sách hồ sơ');
        }

        if ($request->ajax()) {
            $query = Document::with(['contractType', 'assetTemplate', 'user', 'parties'])
                ->select('documents.*');

            // Apply filters
            if ($request->filled('contract_type_id')) {
                $query->where('contract_type_id', $request->contract_type_id);
            }

            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            if ($request->filled('search')) {
                $query->search($request->search);
            }

            // For documents, we'll keep server-side processing due to potentially large datasets
            // but return custom JSON format instead of DataTables format
            $documents = $query->orderBy('created_at', 'desc')->get();

            $data = $documents->map(function ($document) {
                $actions = '';

                if (Auth::user()->hasPermission('documents.view')) {
                    $actions .= '<a href="' . route('documents.show', $document) . '" class="btn btn-sm btn-outline-primary me-1">
                        <i class="ri-eye-line"></i>
                    </a>';
                }

                if (Auth::user()->hasPermission('documents.edit') && $document->isEditable()) {
                    $actions .= '<a href="' . route('documents.edit', $document) . '" class="btn btn-sm btn-outline-warning me-1">
                        <i class="ri-edit-line"></i>
                    </a>';
                }

                if (Auth::user()->hasPermission('documents.export')) {
                    $actions .= '<a href="' . route('documents.export', $document) . '" class="btn btn-sm btn-outline-success me-1">
                        <i class="ri-download-line"></i>
                    </a>';
                }

                if (Auth::user()->hasPermission('documents.delete') && $document->status === 'draft') {
                    $actions .= '<button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteDocument(' . $document->id . ')">
                        <i class="ri-delete-bin-line"></i>
                    </button>';
                }

                $statusClasses = [
                    'draft' => 'bg-label-secondary',
                    'processing' => 'bg-label-warning',
                    'completed' => 'bg-label-success',
                    'cancelled' => 'bg-label-danger',
                ];

                $statusClass = $statusClasses[$document->status] ?? 'bg-label-secondary';
                $statusBadge = '<span class="badge ' . $statusClass . '">' . $document->formatted_status . '</span>';

                return [
                    'id' => $document->id,
                    'title' => $document->title,
                    'contract_type' => $document->contractType ? $document->contractType->name : '-',
                    'parties_count' => $document->parties->count(),
                    'status_badge' => $statusBadge,
                    'created_by' => $document->user ? $document->user->name : '-',
                    'created_at' => $document->created_at->format('d/m/Y H:i'),
                    'action' => $actions,
                    'status' => $document->status
                ];
            });

            return response()->json(['data' => $data]);
        }

        $contractTypes = ContractType::active()->ordered()->get();
        $statusOptions = Document::getStatusOptions();

        return view('documents.index', compact('contractTypes', 'statusOptions'));
    }

    /**
     * Show the wizard form for creating a new document.
     */
    public function wizard()
    {
        // Check permission
        if (!Auth::user()->hasPermission('documents.create')) {
            abort(403, 'Bạn không có quyền tạo hồ sơ mới');
        }

        $contractTypes = ContractType::active()->ordered()->get();

        return view('documents.wizard', compact('contractTypes'));
    }

    /**
     * Get templates by contract type (AJAX)
     */
    public function getTemplatesByContractType(Request $request)
    {
        $contractTypeId = $request->contract_type_id;

        $templates = AssetTemplate::where('contract_type_id', $contractTypeId)
            ->active()
            ->ordered()
            ->get();

        return response()->json($templates);
    }

    /**
     * Get template fields (AJAX)
     */
    public function getTemplateFields(Request $request)
    {
        $templateId = $request->template_id;

        $template = AssetTemplate::with(['activeAssetFields' => function ($query) {
            $query->orderBy('template_fields.sort_order');
        }])->find($templateId);

        if (!$template) {
            return response()->json(['error' => 'Template not found'], 404);
        }

        $groupedFields = $template->getGroupedFields();

        return response()->json([
            'template' => $template,
            'grouped_fields' => $groupedFields,
        ]);
    }

    /**
     * Search parties (AJAX)
     */
    public function searchParties(Request $request)
    {
        $search = $request->search;

        $parties = DocumentParty::search($search)
            ->select('full_name', 'birth_year', 'id_number', 'id_type', 'current_address', 'phone', 'email')
            ->distinct()
            ->limit(10)
            ->get();

        return response()->json($parties);
    }

    /**
     * Search assets (AJAX)
     */
    public function searchAssets(Request $request)
    {
        $search = $request->search;

        $assets = DocumentAsset::search($search)
            ->select('asset_name', 'asset_code', 'field_values', 'estimated_value')
            ->distinct()
            ->limit(10)
            ->get();

        return response()->json($assets);
    }

    /**
     * Store a newly created document from wizard.
     */
    public function storeFromWizard(Request $request)
    {
        // Check permission
        if (!Auth::user()->hasPermission('documents.create')) {
            abort(403, 'Bạn không có quyền tạo hồ sơ mới');
        }

        $request->validate([
            'contract_type_id' => 'required|exists:contract_types,id',
            'asset_template_id' => 'required|exists:asset_templates,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'parties' => 'required|array|min:1',
            'parties.*.full_name' => 'required|string|max:255',
            'parties.*.birth_year' => 'required|integer|min:1900|max:' . date('Y'),
            'parties.*.id_number' => 'required|string|max:20',
            'parties.*.current_address' => 'required|string',
            'assets' => 'required|array|min:1',
            'assets.*.asset_name' => 'required|string|max:255',
        ]);

        try {
            DB::beginTransaction();

            // Create document
            $document = Document::create([
                'contract_type_id' => $request->contract_type_id,
                'asset_template_id' => $request->asset_template_id,
                'user_id' => Auth::id(),
                'title' => $request->title,
                'description' => $request->description,
                'status' => 'draft',
                'wizard_data' => $request->all(),
            ]);

            // Create parties
            foreach ($request->parties as $index => $partyData) {
                DocumentParty::create([
                    'document_id' => $document->id,
                    'party_type' => $partyData['party_type'] ?? 'party_a',
                    'full_name' => $partyData['full_name'],
                    'birth_year' => $partyData['birth_year'],
                    'id_number' => $partyData['id_number'],
                    'id_type' => $partyData['id_type'] ?? 'cccd',
                    'current_address' => $partyData['current_address'],
                    'permanent_address' => $partyData['permanent_address'] ?? null,
                    'phone' => $partyData['phone'] ?? null,
                    'email' => $partyData['email'] ?? null,
                    'gender' => $partyData['gender'] ?? null,
                    'occupation' => $partyData['occupation'] ?? null,
                    'notes' => $partyData['notes'] ?? null,
                    'sort_order' => $index,
                ]);
            }

            // Create assets
            foreach ($request->assets as $index => $assetData) {
                DocumentAsset::create([
                    'document_id' => $document->id,
                    'asset_name' => $assetData['asset_name'],
                    'asset_code' => $assetData['asset_code'] ?? null,
                    'field_values' => $assetData['field_values'] ?? [],
                    'description' => $assetData['description'] ?? null,
                    'estimated_value' => $assetData['estimated_value'] ?? null,
                    'currency' => $assetData['currency'] ?? 'VND',
                    'notes' => $assetData['notes'] ?? null,
                    'sort_order' => $index,
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Hồ sơ đã được tạo thành công',
                'document_id' => $document->id,
                'redirect_url' => route('documents.show', $document),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tạo hồ sơ: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Document $document)
    {
        // Check permission
        if (!Auth::user()->hasPermission('documents.view')) {
            abort(403, 'Bạn không có quyền xem hồ sơ');
        }

        $document->load(['contractType', 'assetTemplate', 'user', 'parties', 'assets']);

        return view('documents.show', compact('document'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Document $document)
    {
        // Check permission
        if (!Auth::user()->hasPermission('documents.delete')) {
            abort(403, 'Bạn không có quyền xóa hồ sơ');
        }

        if ($document->status !== 'draft') {
            return response()->json([
                'success' => false,
                'message' => 'Chỉ có thể xóa hồ sơ ở trạng thái nháp',
            ], 400);
        }

        try {
            $document->delete();

            return response()->json([
                'success' => true,
                'message' => 'Hồ sơ đã được xóa thành công',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa hồ sơ: ' . $e->getMessage(),
            ], 500);
        }
    }
}
