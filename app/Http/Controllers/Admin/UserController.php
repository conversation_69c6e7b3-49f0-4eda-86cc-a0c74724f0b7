<?php

namespace App\Http\Controllers\Admin;


use App\Models\User;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;


class UserController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        return view('admin.users.index');
    }

    /**
     * Get users data for DataTables
     */
    public function getData(Request $request)
    {
        $query = User::with('role');

        // Handle custom filters
        if ($request->has('role_filter') && !empty($request->role_filter)) {
            $query->whereHas('role', function ($roleQuery) use ($request) {
                $roleQuery->where('name', $request->role_filter);
            });
        }

        if ($request->has('status_filter') && !empty($request->status_filter)) {
            $query->where('status', $request->status_filter);
        }

        // Handle search
        if ($request->has('search') && !empty($request->search['value'])) {
            $searchValue = $request->search['value'];
            $query->where(function ($q) use ($searchValue) {
                $q->where('name', 'like', "%{$searchValue}%")
                  ->orWhere('email', 'like', "%{$searchValue}%")
                  ->orWhereHas('role', function ($roleQuery) use ($searchValue) {
                      $roleQuery->where('name', 'like', "%{$searchValue}%");
                  });
            });
        }

        // Handle column search
        if ($request->has('columns')) {
            foreach ($request->columns as $column) {
                if (!empty($column['search']['value'])) {
                    $searchValue = $column['search']['value'];
                    switch ($column['data']) {
                        case 'role_name':
                            $query->whereHas('role', function ($roleQuery) use ($searchValue) {
                                $roleQuery->where('name', 'like', "%{$searchValue}%");
                            });
                            break;
                        case 'status':
                            $query->where('status', 'like', "%{$searchValue}%");
                            break;
                    }
                }
            }
        }

        // Handle ordering
        if ($request->has('order')) {
            $orderColumn = $request->columns[$request->order[0]['column']]['data'];
            $orderDirection = $request->order[0]['dir'];

            switch ($orderColumn) {
                case 'role_name':
                    $query->join('roles', 'users.role_id', '=', 'roles.id')
                          ->orderBy('roles.name', $orderDirection)
                          ->select('users.*');
                    break;
                default:
                    if (in_array($orderColumn, ['id', 'name', 'email', 'status', 'created_at'])) {
                        $query->orderBy($orderColumn, $orderDirection);
                    }
                    break;
            }
        } else {
            $query->orderBy('id', 'desc');
        }

        // Get total count before pagination
        $totalRecords = User::count();
        $filteredRecords = $query->count();

        // Handle pagination
        $start = $request->start ?? 0;
        $length = $request->length ?? 10;
        $users = $query->skip($start)->take($length)->get();

        // Format data for DataTables
        $data = $users->map(function ($user) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role_name' => $user->role ? $user->role->name : 'No Role',
                'status' => $user->status,
                'status_badge' => '<span class="badge ' . ($user->status === 'active' ? 'bg-label-success' : 'bg-label-secondary') . '">' . ucfirst($user->status) . '</span>',
                'created_at' => $user->created_at->format('Y-m-d H:i:s'),
                'actions' => '
                    <div class="dropdown">
                        <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                            <i class="ri-more-2-line"></i>
                        </button>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="' . route('admin.users.edit', $user->id) . '">
                                <i class="ri-pencil-line me-1"></i> Edit
                            </a>
                            <a class="dropdown-item text-danger" href="javascript:void(0);" onclick="deleteUser(' . $user->id . ')">
                                <i class="ri-delete-bin-7-line me-1"></i> Delete
                            </a>
                        </div>
                    </div>'
            ];
        });

        return response()->json([
            'draw' => intval($request->draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $roles = Role::all();
        return view('admin.users.create', compact('roles'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role_id' => 'required|exists:roles,id',
            'status' => 'required|in:active,inactive',
        ]);

        User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role_id' => $request->role_id,
            'status' => $request->status,
            'email_verified_at' => now(),
        ]);

        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        $user->load('role');
        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $roles = Role::all();
        return view('admin.users.edit', compact('user', 'roles'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'role_id' => 'required|exists:roles,id',
            'status' => 'required|in:active,inactive',
        ]);

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'role_id' => $request->role_id,
            'status' => $request->status,
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        return redirect()->route('admin.users.index')
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        if ($user->id === auth()->id()) {
            return response()->json(['error' => 'You cannot delete yourself.'], 422);
        }

        $user->delete();

        return response()->json(['success' => 'User deleted successfully.']);
    }

    /**
     * Reset user password
     */
    public function resetPassword(Request $request, User $user)
    {
        $request->validate([
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user->update([
            'password' => Hash::make($request->password),
        ]);

        return redirect()->back()->with('success', 'Password reset successfully.');
    }
}
